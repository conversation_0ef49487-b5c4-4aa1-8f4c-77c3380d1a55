/* Checkout Page Styles */
.checkout-page {
  min-height: calc(100vh - 90px);

  padding: var(--heading6) 0;
}

.checkout-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--basefont);
}

.checkout-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 3rem;
  align-items: start;
margin-top: 2.5rem;
}

/* Left Section - Checkout Form */
.checkout-left {
  display: flex;
  justify-content: center;
}

.checkout-form-container {
  background-color: var(--white);
  border-radius: var(--border-radius-large);

  width: 100%;
 
  transition: box-shadow 0.3s ease;
}
.leftborderdiv{
  border: 1px solid var(--light-gray);
border-radius: var(--border-radius-large);
padding: 1.5rem;
}

.checkout-title {
  font-size: var(--heading4);
  font-weight: 600;
  color: var(--secondary-color);
  margin-bottom: 1.5rem;
  text-align: left;
}

.checkout-alert {
  background-color: #fdf2f2;
  border: 1px solid #fecaca;
  border-radius: var(--border-radius);
  padding: 0.875rem 1rem;
  margin-bottom: 2rem;
  color: #dc2626;
  font-size: var(--smallfont);
  text-align: center;
}

/* Sign In Section */
.signin-section {
  margin-top: 2rem;
}
.signin-sectioncontainer{
  display: flex
;
    align-items: center;
    justify-content: space-between;
}
.signin-title {
  font-size: var(--heading6);
  font-weight: 600;
  color: var(--secondary-color);
  margin-bottom: 0.5rem;
}

.signin-subtitle {
  font-size: var(--smallfont);
  color: var(--dark-gray);
  margin-bottom: 1.5rem;
}

.signup-link {
  color: var(--btn-color);
  text-decoration: none;
  font-weight: 500;
}

.signup-link:hover {
  text-decoration: underline;
}

.signin-form {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-input {
  padding: 0.875rem 1rem;
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
  font-size: var(--basefont);
  color: var(--text-color);
  background-color: var(--white);
  transition: border-color 0.3s ease, box-shadow 0.3s ease;
  outline: none;
}

.form-input:focus {
  border-color: var(--btn-color);
  box-shadow: 0 0 0 2px rgba(238, 52, 37, 0.1);
}

.form-input::placeholder {
  color: var(--dark-gray);
  opacity: 0.7;
}





/* Right Section - Order Summary */
.checkout-right {
  display: flex;
  justify-content: center;
  width:100%
}

.order-summary {
  background-color: var(--white);
  border-radius: var(--border-radius-large);


  width: 100%;
  max-width: 500px;
  transition: box-shadow 0.3s ease;
}


.order-title {
  font-size: var(--heading4);
  font-weight: 600;
  color: var(--secondary-color);
  margin-bottom: 2rem;
  text-align: left;
}

/* Item Info Section */
.item-info-section {
  margin-bottom: 2rem;
}

.item-info-title {
  font-size: var(--heading6);
  font-weight: 600;
  color: var(--secondary-color);
  margin-bottom: 1rem;
}

.item-details {
  display: flex;
  gap: 1rem;
  align-items: flex-start;
}

.item-image {
  flex-shrink: 0;
}

.item-thumbnail {
  width: 80px;
  height: 80px;
  border-radius: var(--border-radius);
  object-fit: cover;
  border: 1px solid var(--light-gray);
}

.item-description {
  flex: 1;
}

.item-name {
  font-size: var(--smallfont);
  font-weight: 500;
  color: var(--text-color);
  line-height: 1.4;
  margin: 0;
}

/* Pricing Section */
.pricing-section {
  border-top: 1px solid var(--light-gray);
  padding-top: 1.5rem;
  margin-bottom: 2rem;
}

.price-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
}

.price-row:last-child {
  margin-bottom: 0;
}

.total-row {
  border-top: 1px solid var(--light-gray);
  padding-top: 0.75rem;
  margin-top: 0.75rem;
  font-weight: 600;
}

.price-label {
  font-size: var(--basefont);
  color: var(--text-color);
}

.price-value {
  font-size: var(--basefont);
  color: var(--text-color);
  font-weight: 500;
}

.total-row .price-label,
.total-row .price-value {
  font-weight: 600;
  color: var(--secondary-color);
}



/* Responsive Design */
@media (max-width: 1200px) {
  .checkout-container {
    max-width: 100%;
    padding: 0 1.5rem;
  }
}

@media (max-width: 1024px) {
  .checkout-container {
    padding: 0 var(--smallfont);
  }

  .checkout-content {
    gap: 2rem;
  }


}

@media (max-width: 768px) {
  .checkout-content {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .checkout-form-container{
   
    max-width: 100%;
    padding: 1.5rem;
  }

  .checkout-title,
  .order-title {
    font-size: var(--heading5);
  }

  .item-details {
    gap: 0.75rem;
  }

  .item-thumbnail {
    width: 60px;
    height: 60px;
  }
}

@media (max-width: 640px) {
  .checkout-page {
    padding: var(--basefont) 0;
  }

  .checkout-container {
    padding: 0 1rem;
  }

  .checkout-content {
    gap: 1rem;
  }


}

@media (max-width: 480px) {
  .checkout-container {
    padding: 0 0.75rem;
  }

  .checkout-form-container
   {
    padding: 1rem;
  }

  .checkout-title,
  .order-title {
    font-size: var(--heading6);
    margin-bottom: 1rem;
  }

  .item-thumbnail {
    width: 50px;
    height: 50px;
  }

  .item-name {
    font-size: var(--extrasmallfont);
  }

  .form-input{
    font-size: var(--smallfont);
  }
}

@media (max-width: 360px) {
  .checkout-container {
    padding: 0 0.5rem;
  }



  .item-details {
    flex-direction: column;
    gap: 0.5rem;
    text-align: center;
  }

  .item-thumbnail {
    width: 60px;
    height: 60px;
    margin: 0 auto;
  }
}
